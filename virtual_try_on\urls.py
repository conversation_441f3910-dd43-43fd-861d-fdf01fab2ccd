from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView
from django.http import FileResponse
import os

def serve_test_upload(request):
    """Serve the test upload HTML file"""
    file_path = os.path.join(settings.BASE_DIR, 'test_upload.html')
    return FileResponse(open(file_path, 'rb'), content_type='text/html')

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', TemplateView.as_view(template_name='index.html'), name='home'),
    path('tryron/', TemplateView.as_view(template_name='tryron.html'), name='tryron'),
    path('test_upload.html', serve_test_upload, name='test_upload'),
    path('api/face/', include('face_detection.urls')),
    path('api/models/', include('models_manager.urls')),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)