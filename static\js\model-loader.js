class DjangoModelLoader {
    constructor() {
        // Handle different ways GLTFLoader might be available
        try {
            if (typeof THREE.GLTFLoader !== 'undefined') {
                this.loader = new THREE.GLTFLoader();
            } else if (typeof GLTFLoader !== 'undefined') {
                this.loader = new GLTFLoader();
            } else {
                console.warn('GLTFLoader not found. Using fallback to simple models only.');
                this.loader = null;
            }
        } catch (error) {
            console.warn('Error creating GLTFLoader:', error, 'Using fallback to simple models only.');
            this.loader = null;
        }

        this.loadedModels = new Map();
        this.availableModels = {};
        this.csrfToken = this.getCsrfToken();
        this.currentCategory = 'glasses';
        this.simpleModelGenerator = new SimpleModelGenerator();

        // Enhanced loading features
        this.loadingQueue = [];
        this.maxConcurrentLoads = 2;
        this.currentLoads = 0;
        this.modelCache = new Map();
        this.preloadedModels = new Set();

        // Performance optimization
        this.compressionSupported = this.checkCompressionSupport();
        this.textureCache = new Map();
    }
    
    getCsrfToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }
        return '';
    }

    checkCompressionSupport() {
        // Check for Draco compression support
        try {
            return typeof THREE.DRACOLoader !== 'undefined';
        } catch (error) {
            return false;
        }
    }

    setupDracoLoader() {
        if (this.compressionSupported && this.loader) {
            try {
                const dracoLoader = new THREE.DRACOLoader();
                dracoLoader.setDecoderPath('/static/js/libs/draco/');
                this.loader.setDRACOLoader(dracoLoader);
            } catch (error) {
                console.warn('Failed to setup Draco loader:', error);
            }
        }
    }
    
    async loadAvailableModels() {
        try {
            const response = await fetch('/api/models/list/', {
                headers: {
                    'X-CSRFToken': this.csrfToken
                }
            });
            const result = await response.json();

            if (result.success) {
                // If no models are available from the server, use simple models
                if (Object.keys(result.models).length === 0 ||
                    (result.models.glasses && result.models.glasses.length === 0 &&
                     result.models.hats && result.models.hats.length === 0)) {
                    console.log('No server models found, using simple geometric models');
                    this.availableModels = this.simpleModelGenerator.getAvailableModels();
                } else {
                    this.availableModels = result.models;

                    // Merge with simple models for fallback
                    const simpleModels = this.simpleModelGenerator.getAvailableModels();
                    for (const category in simpleModels) {
                        if (!this.availableModels[category]) {
                            this.availableModels[category] = [];
                        }
                        this.availableModels[category] = [
                            ...simpleModels[category],
                            ...this.availableModels[category]
                        ];
                    }
                }

                // Start preloading popular models in background
                this.preloadPopularModels();

                return this.availableModels;
            } else {
                console.error('Failed to load models:', result.error);
                // Fallback to simple models
                this.availableModels = this.simpleModelGenerator.getAvailableModels();
                return this.availableModels;
            }
        } catch (error) {
            console.error('Error loading models:', error);
            // Fallback to simple models on error
            this.availableModels = this.simpleModelGenerator.getAvailableModels();
            return this.availableModels;
        }
    }

    async loadCategories() {
        try {
            const response = await fetch('/api/models/categories/', {
                headers: {
                    'X-CSRFToken': this.csrfToken
                }
            });
            const categories = await response.json();

            if (Array.isArray(categories)) {
                this.categories = categories;
                this.populateCategorySelect();
                return categories;
            } else {
                throw new Error('Invalid categories response');
            }
        } catch (error) {
            console.error('Error loading categories:', error);
            this.categories = [];
            return [];
        }
    }

    populateCategorySelect() {
        const categorySelect = document.getElementById('model-category');
        if (!categorySelect) return;

        // Clear existing options except the first one
        while (categorySelect.children.length > 1) {
            categorySelect.removeChild(categorySelect.lastChild);
        }

        // Add category options
        this.categories.forEach(category => {
            if (category.is_active) {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categorySelect.appendChild(option);
            }
        });
    }

    async preloadPopularModels() {
        // Preload featured and highly-rated models for better UX
        const modelsToPreload = [];

        for (const category in this.availableModels) {
            const categoryModels = this.availableModels[category];
            const featuredModels = categoryModels
                .filter(model => model.is_featured || (model.average_rating && model.average_rating >= 4))
                .slice(0, 2); // Preload top 2 per category

            modelsToPreload.push(...featuredModels);
        }

        // Preload in background without blocking UI
        for (const model of modelsToPreload) {
            if (model.file_url && !this.preloadedModels.has(model.id)) {
                this.preloadedModels.add(model.id);
                // Load in background, don't await to avoid blocking
                setTimeout(() => {
                    this.loadModel(model.file_url, model.id).catch(error => {
                        console.warn(`Failed to preload model ${model.id}:`, error);
                    });
                }, Math.random() * 2000); // Stagger preloading
            }
        }
    }
    
    async loadModel(modelUrl, modelId = null) {
        // Check if this is a simple model (no URL)
        if (!modelUrl && modelId) {
            const simpleModel = this.simpleModelGenerator.getModel(modelId);
            if (simpleModel) {
                return simpleModel;
            }
        }

        // Check cache first
        const cacheKey = modelUrl || modelId;
        if (this.modelCache.has(cacheKey)) {
            return this.modelCache.get(cacheKey).clone();
        }

        if (this.loadedModels.has(modelUrl)) {
            const model = this.loadedModels.get(modelUrl).clone();
            this.modelCache.set(cacheKey, model);
            return model;
        }

        // If no GLTFLoader available, fall back to simple models
        if (!this.loader) {
            console.warn('GLTFLoader not available, using simple model fallback');
            const simpleModel = this.simpleModelGenerator.getModel(modelId || 'simple-glasses');
            if (simpleModel) {
                this.modelCache.set(cacheKey, simpleModel);
                return simpleModel;
            }
            throw new Error('No model loader available and no simple model found');
        }

        // Setup Draco loader if not already done
        this.setupDracoLoader();

        // Queue loading if too many concurrent loads
        if (this.currentLoads >= this.maxConcurrentLoads) {
            return new Promise((resolve, reject) => {
                this.loadingQueue.push({ modelUrl, modelId, resolve, reject });
            });
        }

        return this.loadModelInternal(modelUrl, modelId);
    }

    async loadModelInternal(modelUrl, modelId) {
        this.currentLoads++;

        try {
            return await new Promise((resolve, reject) => {
                this.loader.load(
                    modelUrl,
                    (gltf) => {
                        const model = gltf.scene;
                        this.optimizeModel(model);
                        this.loadedModels.set(modelUrl, model);

                        const clonedModel = model.clone();
                        const cacheKey = modelUrl || modelId;
                        this.modelCache.set(cacheKey, clonedModel);

                        resolve(clonedModel);
                        this.processLoadingQueue();
                    },
                    (progress) => {
                        const percent = (progress.loaded / progress.total) * 100;
                        console.log(`Loading ${modelId || 'model'}: ${percent.toFixed(1)}%`);
                    },
                    (error) => {
                        console.error('Error loading model:', error);
                        // Fall back to simple model on error
                        const simpleModel = this.simpleModelGenerator.getModel(modelId || 'simple-glasses');
                        if (simpleModel) {
                            const cacheKey = modelUrl || modelId;
                            this.modelCache.set(cacheKey, simpleModel);
                            resolve(simpleModel);
                        } else {
                            reject(error);
                        }
                        this.processLoadingQueue();
                    }
                );
            });
        } finally {
            this.currentLoads--;
        }
    }

    processLoadingQueue() {
        if (this.loadingQueue.length > 0 && this.currentLoads < this.maxConcurrentLoads) {
            const { modelUrl, modelId, resolve, reject } = this.loadingQueue.shift();
            this.loadModelInternal(modelUrl, modelId).then(resolve).catch(reject);
        }
    }
    
    optimizeModel(model) {
        // Enhanced model optimization for real-time rendering
        model.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;

                if (child.material) {
                    // Optimize material for real-time rendering
                    child.material.needsUpdate = true;

                    if (child.material.transparent) {
                        child.material.alphaTest = 0.1;
                    }

                    // Optimize texture settings if present
                    if (child.material.map) {
                        child.material.map.generateMipmaps = true;
                        child.material.map.minFilter = THREE.LinearMipmapLinearFilter;
                        child.material.map.magFilter = THREE.LinearFilter;

                        // Cache texture
                        const textureKey = child.material.map.uuid;
                        if (!this.textureCache.has(textureKey)) {
                            this.textureCache.set(textureKey, child.material.map);
                        }
                    }

                    // Optimize material properties for performance
                    if (child.material.isMeshStandardMaterial) {
                        child.material.roughness = child.material.roughness || 0.5;
                        child.material.metalness = child.material.metalness || 0.0;
                    }
                }

                if (child.geometry) {
                    child.geometry.computeBoundingBox();
                    child.geometry.computeBoundingSphere();

                    // Optimize geometry for smaller models
                    if (child.geometry.attributes.position.count < 5000) {
                        try {
                            child.geometry = child.geometry.clone();
                            if (child.geometry.mergeVertices) {
                                child.geometry.mergeVertices();
                            }
                        } catch (error) {
                            console.warn('Failed to optimize geometry:', error);
                        }
                    }
                }

                // Enable frustum culling for performance
                child.frustumCulled = true;
            }
        });

        // Reset transform
        model.scale.set(1, 1, 1);
        model.position.set(0, 0, 0);
        model.rotation.set(0, 0, 0);

        // Enable matrix auto-update for real-time positioning
        model.matrixAutoUpdate = true;

        return model;
    }
    
    async uploadModel(file, name, category, description = '') {
        try {
            console.log('Starting upload:', { fileName: file.name, name, category, description });

            // Validate file type
            const allowedExtensions = ['glb', 'gltf'];
            const fileExtension = file.name.split('.').pop().toLowerCase();

            if (!allowedExtensions.includes(fileExtension)) {
                throw new Error(`Invalid file type. Only ${allowedExtensions.join(', ')} files are allowed.`);
            }

            // Validate file size (max 50MB)
            const maxSize = 50 * 1024 * 1024; // 50MB
            if (file.size > maxSize) {
                throw new Error('File size too large. Maximum size is 50MB.');
            }

            console.log('File validation passed');

            const formData = new FormData();
            formData.append('model_file', file);
            formData.append('name', name);
            formData.append('category', category);
            formData.append('description', description);

            console.log('FormData prepared, sending request...');

            // Use the correct REST API endpoint
            const response = await fetch('/api/models/models/', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': this.csrfToken
                }
            });

            console.log('Response received:', response.status, response.statusText);

            if (!response.ok) {
                const errorText = await response.text();
                let errorMessage = 'Upload failed';

                try {
                    const errorData = JSON.parse(errorText);
                    if (errorData.model_file) {
                        errorMessage = errorData.model_file[0];
                    } else if (errorData.detail) {
                        errorMessage = errorData.detail;
                    } else if (errorData.error) {
                        errorMessage = errorData.error;
                    }
                } catch (e) {
                    errorMessage = `Upload failed: ${response.status} ${response.statusText}`;
                }

                throw new Error(errorMessage);
            }

            const result = await response.json();

            // Reload available models
            await this.loadAvailableModels();

            return {
                success: true,
                model: result,
                message: 'Model uploaded successfully!'
            };

        } catch (error) {
            console.error('Model upload error:', error);
            throw error;
        }
    }
    
    async deleteModel(modelId) {
        try {
            const response = await fetch(`/api/models/delete/${modelId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': this.csrfToken
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                await this.loadAvailableModels();
                return true;
            } else {
                throw new Error(result.error || 'Delete failed');
            }
        } catch (error) {
            console.error('Model delete error:', error);
            return false;
        }
    }
    
    renderModelsGrid(container, category = null) {
        if (!container) return;
        
        const targetCategory = category || this.currentCategory;
        const models = this.availableModels[targetCategory] || [];
        
        container.innerHTML = '';
        
        if (models.length === 0) {
            container.innerHTML = '<p style="text-align: center; color: #ccc;">No models available</p>';
            return;
        }
        
        models.forEach(model => {
            const modelElement = document.createElement('div');
            modelElement.className = 'model-item';
            modelElement.dataset.modelId = model.id;
            modelElement.dataset.modelUrl = model.file_url;
            
            modelElement.innerHTML = `
                ${model.thumbnail_url ? 
                    `<img src="${model.thumbnail_url}" alt="${model.name}">` : 
                    '<div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 5px; margin-bottom: 0.5rem;"></div>'
                }
                <div class="model-name">${model.name}</div>
                <div class="model-size" style="font-size: 10px; opacity: 0.7;">${model.file_size_mb}MB</div>
            `;
            
            modelElement.addEventListener('click', () => {
                // Remove active class from all models
                container.querySelectorAll('.model-item').forEach(item => {
                    item.classList.remove('active', 'selected');
                });

                // Add active class to clicked model with animation
                modelElement.classList.add('active', 'selected');

                // Add fade-in animation to the element
                modelElement.classList.add('fade-in');
                setTimeout(() => {
                    modelElement.classList.remove('fade-in');
                }, 500);

                // Trigger model selection event
                const event = new CustomEvent('modelSelected', {
                    detail: {
                        model: model,
                        category: targetCategory
                    }
                });
                container.dispatchEvent(event);
            });
            
            container.appendChild(modelElement);
        });
    }
}