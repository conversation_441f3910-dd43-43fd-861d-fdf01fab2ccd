class DjangoFaceTracker {
    constructor() {
        this.isDetecting = false;
        this.lastDetection = null;
        this.detectionInterval = null;
        this.csrfToken = this.getCsrfToken();

        // Enhanced tracking parameters
        this.detectionQueue = [];
        this.maxQueueSize = 3;
        this.adaptiveInterval = 200; // Start with 200ms
        this.minInterval = 100;
        this.maxInterval = 500;
        this.performanceMetrics = {
            averageProcessingTime: 200,
            frameDrops: 0,
            successRate: 1.0
        };

        // Frame optimization
        this.lastFrameTime = 0;
        this.frameSkipThreshold = 33; // Skip frames if less than 33ms apart (30fps)
    }
    
    getCsrfToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }
        return '';
    }
    
    async detectFace(videoElement) {
        const currentTime = performance.now();

        // Skip frame if too soon since last detection (frame rate limiting)
        if (currentTime - this.lastFrameTime < this.frameSkipThreshold) {
            return this.lastDetection;
        }

        if (this.isDetecting || !videoElement || videoElement.readyState !== videoElement.HAVE_ENOUGH_DATA) {
            return this.lastDetection;
        }

        // Skip if queue is full
        if (this.detectionQueue.length >= this.maxQueueSize) {
            this.performanceMetrics.frameDrops++;
            return this.lastDetection;
        }

        this.lastFrameTime = currentTime;
        this.isDetecting = true;

        try {
            const startTime = performance.now();

            // Capture frame from video with optimized quality
            const canvas = document.createElement('canvas');
            canvas.width = videoElement.videoWidth;
            canvas.height = videoElement.videoHeight;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(videoElement, 0, 0);

            // Convert to blob with adaptive quality
            const quality = this.getAdaptiveQuality();
            const blob = await new Promise(resolve =>
                canvas.toBlob(resolve, 'image/jpeg', quality)
            );

            // Send to Django backend
            const formData = new FormData();
            formData.append('image', blob, 'frame.jpg');

            const response = await fetch('/api/face/detect/', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': this.csrfToken
                }
            });

            const result = await response.json();
            const processingTime = performance.now() - startTime;

            // Update performance metrics
            this.updatePerformanceMetrics(processingTime, result.success);

            if (result.success) {
                // Enhanced landmark data with all available information
                const landmarks = {
                    left_eye: result.landmarks.left_eye_center,
                    right_eye: result.landmarks.right_eye_center,
                    nose_tip: result.landmarks.nose_tip,
                    forehead: result.landmarks.forehead_center,
                    chin: result.landmarks.chin_center,
                    face_width: result.measurements.face_width,
                    face_height: result.measurements.face_height,
                    confidence: result.confidence,
                    // Additional data for enhanced positioning
                    measurements: result.measurements,
                    orientation: result.orientation,
                    accessory_positions: result.accessory_positions,
                    all_landmarks: result.landmarks
                };

                this.lastDetection = landmarks;
                return landmarks;
            } else {
                this.lastDetection = null;
                return null;
            }

        } catch (error) {
            console.error('Face detection error:', error);
            this.performanceMetrics.frameDrops++;
            return null;
        } finally {
            this.isDetecting = false;
        }
    }
    
    getAdaptiveQuality() {
        // Adjust quality based on performance metrics
        if (this.performanceMetrics.averageProcessingTime > 300) {
            return 0.6; // Lower quality for better performance
        } else if (this.performanceMetrics.averageProcessingTime > 200) {
            return 0.7;
        } else {
            return 0.8; // High quality when performance is good
        }
    }

    updatePerformanceMetrics(processingTime, success) {
        // Update average processing time with exponential smoothing
        this.performanceMetrics.averageProcessingTime =
            this.performanceMetrics.averageProcessingTime * 0.8 + processingTime * 0.2;

        // Update success rate
        this.performanceMetrics.successRate =
            this.performanceMetrics.successRate * 0.9 + (success ? 1 : 0) * 0.1;

        // Adapt detection interval based on performance
        this.adaptDetectionInterval();
    }

    adaptDetectionInterval() {
        const targetProcessingTime = 150; // Target 150ms processing time
        const currentTime = this.performanceMetrics.averageProcessingTime;

        if (currentTime > targetProcessingTime * 1.5) {
            // Slow down detection if processing is too slow
            this.adaptiveInterval = Math.min(this.adaptiveInterval + 50, this.maxInterval);
        } else if (currentTime < targetProcessingTime * 0.8) {
            // Speed up detection if processing is fast
            this.adaptiveInterval = Math.max(this.adaptiveInterval - 25, this.minInterval);
        }
    }

    startContinuousDetection(videoElement, callback, interval = null) {
        this.stopContinuousDetection();

        // Use adaptive interval if no specific interval provided
        const detectionInterval = interval || this.adaptiveInterval;

        this.detectionInterval = setInterval(async () => {
            const landmarks = await this.detectFace(videoElement);
            if (callback && typeof callback === 'function') {
                callback(landmarks);
            }
        }, detectionInterval);
    }
    
    stopContinuousDetection() {
        if (this.detectionInterval) {
            clearInterval(this.detectionInterval);
            this.detectionInterval = null;
        }
    }
    
    async getDetectionHistory() {
        try {
            const response = await fetch('/api/face/history/', {
                headers: {
                    'X-CSRFToken': this.csrfToken
                }
            });
            const result = await response.json();
            return result.detections || [];
        } catch (error) {
            console.error('Error fetching detection history:', error);
            return [];
        }
    }
    
    async clearSession() {
        try {
            const response = await fetch('/api/face/clear-session/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.csrfToken
                }
            });
            const result = await response.json();
            return result.success || false;
        } catch (error) {
            console.error('Error clearing session:', error);
            return false;
        }
    }
}