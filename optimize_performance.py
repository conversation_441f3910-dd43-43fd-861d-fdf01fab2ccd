#!/usr/bin/env python3
"""
Performance optimization script for Virtual Try-On system
Analyzes and optimizes system performance for real-time face detection
"""

import os
import sys
import django
import time
import json
from pathlib import Path

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'virtual_try_on.settings')
django.setup()

from django.conf import settings
from face_detection.utils import face_tracker
import cv2
import numpy as np

class PerformanceOptimizer:
    def __init__(self):
        self.results = {}
        
    def benchmark_face_detection(self, num_tests=50):
        """Benchmark face detection performance"""
        print("Benchmarking face detection performance...")
        
        # Create test images of different sizes
        test_sizes = [
            (320, 240),   # Small
            (640, 480),   # Medium
            (1280, 720),  # Large
        ]
        
        for width, height in test_sizes:
            print(f"\nTesting {width}x{height} resolution:")
            
            # Create random test image
            test_image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
            _, img_encoded = cv2.imencode('.jpg', test_image, [cv2.IMWRITE_JPEG_QUALITY, 80])
            img_bytes = img_encoded.tobytes()
            
            times = []
            for i in range(num_tests):
                start_time = time.time()
                result = face_tracker.detect_face_landmarks(img_bytes)
                processing_time = (time.time() - start_time) * 1000
                times.append(processing_time)
                
                if i % 10 == 0:
                    print(f"  Progress: {i+1}/{num_tests}")
            
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            std_dev = np.std(times)
            
            self.results[f"{width}x{height}"] = {
                'avg_time': avg_time,
                'min_time': min_time,
                'max_time': max_time,
                'std_dev': std_dev,
                'theoretical_fps': 1000 / avg_time
            }
            
            print(f"  Average: {avg_time:.2f}ms")
            print(f"  Min: {min_time:.2f}ms")
            print(f"  Max: {max_time:.2f}ms")
            print(f"  Std Dev: {std_dev:.2f}ms")
            print(f"  Theoretical FPS: {1000/avg_time:.1f}")
    
    def test_compression_settings(self):
        """Test different image compression settings"""
        print("\nTesting image compression settings...")
        
        # Create test image
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        quality_levels = [50, 60, 70, 80, 90, 95]
        
        for quality in quality_levels:
            _, img_encoded = cv2.imencode('.jpg', test_image, [cv2.IMWRITE_JPEG_QUALITY, quality])
            img_bytes = img_encoded.tobytes()
            
            # Test processing time
            times = []
            for _ in range(10):
                start_time = time.time()
                result = face_tracker.detect_face_landmarks(img_bytes)
                processing_time = (time.time() - start_time) * 1000
                times.append(processing_time)
            
            avg_time = sum(times) / len(times)
            file_size = len(img_bytes)
            
            print(f"  Quality {quality}%: {avg_time:.2f}ms, Size: {file_size/1024:.1f}KB")
    
    def optimize_mediapipe_settings(self):
        """Test different MediaPipe settings for optimal performance"""
        print("\nTesting MediaPipe optimization settings...")
        
        # Test different confidence thresholds
        confidence_levels = [0.5, 0.6, 0.7, 0.8, 0.9]
        
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        _, img_encoded = cv2.imencode('.jpg', test_image)
        img_bytes = img_encoded.tobytes()
        
        for confidence in confidence_levels:
            # Temporarily modify face tracker settings
            original_confidence = face_tracker.face_mesh.min_detection_confidence
            face_tracker.face_mesh.min_detection_confidence = confidence
            
            times = []
            for _ in range(20):
                start_time = time.time()
                result = face_tracker.detect_face_landmarks(img_bytes)
                processing_time = (time.time() - start_time) * 1000
                times.append(processing_time)
            
            avg_time = sum(times) / len(times)
            print(f"  Confidence {confidence}: {avg_time:.2f}ms")
            
            # Restore original setting
            face_tracker.face_mesh.min_detection_confidence = original_confidence
    
    def generate_recommendations(self):
        """Generate performance optimization recommendations"""
        print("\n" + "="*50)
        print("PERFORMANCE OPTIMIZATION RECOMMENDATIONS")
        print("="*50)
        
        # Analyze results
        if '640x480' in self.results:
            medium_res = self.results['640x480']
            
            if medium_res['avg_time'] < 100:
                print("✓ Excellent performance detected!")
                print("  - Current settings are optimal for real-time use")
                print("  - Consider enabling higher quality features")
            elif medium_res['avg_time'] < 200:
                print("✓ Good performance detected!")
                print("  - System is suitable for real-time use")
                print("  - Minor optimizations may improve smoothness")
            else:
                print("⚠ Performance optimization needed!")
                print("  - Consider the following optimizations:")
        
        print("\nRECOMMENDED OPTIMIZATIONS:")
        
        print("\n1. Image Resolution:")
        print("   - Use 640x480 for best balance of quality and performance")
        print("   - Consider 320x240 for low-end devices")
        print("   - Avoid resolutions above 1280x720 for real-time use")
        
        print("\n2. Image Compression:")
        print("   - Use JPEG quality 70-80% for optimal balance")
        print("   - Lower quality (60%) for performance-critical applications")
        print("   - Higher quality (90%) only if processing time allows")
        
        print("\n3. Detection Frequency:")
        if '640x480' in self.results:
            avg_time = self.results['640x480']['avg_time']
            recommended_interval = max(100, avg_time * 1.5)
            print(f"   - Recommended detection interval: {recommended_interval:.0f}ms")
            print(f"   - This allows for {1000/recommended_interval:.1f} FPS detection rate")
        
        print("\n4. MediaPipe Settings:")
        print("   - min_detection_confidence: 0.7 (balanced)")
        print("   - min_tracking_confidence: 0.5 (smooth tracking)")
        print("   - refine_landmarks: True (better accuracy)")
        
        print("\n5. Browser Optimizations:")
        print("   - Enable hardware acceleration in browser")
        print("   - Use WebGL for 3D rendering")
        print("   - Implement frame skipping for consistent performance")
        
        print("\n6. System Optimizations:")
        print("   - Ensure adequate CPU resources")
        print("   - Close unnecessary applications")
        print("   - Use SSD storage for faster model loading")
    
    def save_results(self):
        """Save benchmark results to file"""
        results_file = Path('performance_results.json')
        
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        print(f"\nResults saved to: {results_file}")
    
    def run_full_optimization(self):
        """Run complete performance optimization suite"""
        print("Starting performance optimization analysis...")
        print("This may take a few minutes...\n")
        
        self.benchmark_face_detection()
        self.test_compression_settings()
        self.optimize_mediapipe_settings()
        self.generate_recommendations()
        self.save_results()
        
        print("\n" + "="*50)
        print("Performance optimization analysis complete!")
        print("Check performance_results.json for detailed metrics.")

def main():
    """Main optimization function"""
    optimizer = PerformanceOptimizer()
    
    print("Virtual Try-On Performance Optimizer")
    print("="*40)
    
    try:
        optimizer.run_full_optimization()
    except KeyboardInterrupt:
        print("\nOptimization interrupted by user.")
    except Exception as e:
        print(f"\nError during optimization: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
