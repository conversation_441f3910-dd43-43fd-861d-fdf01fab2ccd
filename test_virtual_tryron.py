#!/usr/bin/env python3
"""
Test script for Virtual Try-On system
Tests face detection, model loading, and real-time performance
"""

import os
import sys
import django
import time
from pathlib import Path

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'virtual_try_on.settings')
django.setup()

from face_detection.utils import face_tracker
from models_manager.models import AccessoryModel, AccessoryCategory
import cv2
import numpy as np

def test_face_detection():
    """Test face detection with a sample image"""
    print("Testing face detection...")
    
    # Create a simple test image (you can replace with actual image)
    test_image = np.zeros((480, 640, 3), dtype=np.uint8)
    cv2.circle(test_image, (320, 240), 100, (255, 255, 255), -1)  # Simple white circle
    
    # Convert to bytes
    _, img_encoded = cv2.imencode('.jpg', test_image)
    img_bytes = img_encoded.tobytes()
    
    # Test detection
    start_time = time.time()
    result = face_tracker.detect_face_landmarks(img_bytes)
    processing_time = (time.time() - start_time) * 1000
    
    print(f"Face detection processing time: {processing_time:.2f}ms")
    
    if result:
        print("✓ Face detection working")
        print(f"  - Confidence: {result['confidence']:.2f}")
        print(f"  - Processing time: {result['processing_time']:.2f}ms")
        print(f"  - Landmarks detected: {len(result['landmarks'])}")
        return True
    else:
        print("✗ No face detected (expected for test image)")
        return False

def test_model_management():
    """Test model management system"""
    print("\nTesting model management...")
    
    # Test categories
    categories = AccessoryCategory.objects.all()
    print(f"✓ Found {categories.count()} accessory categories")
    
    # Test models
    models = AccessoryModel.objects.all()
    print(f"✓ Found {models.count()} accessory models")
    
    for category in categories:
        category_models = models.filter(category=category)
        print(f"  - {category.name}: {category_models.count()} models")
    
    return True

def test_api_endpoints():
    """Test API endpoints"""
    print("\nTesting API endpoints...")

    if not REQUESTS_AVAILABLE:
        print("⚠ Requests library not available, skipping API tests")
        print("  Install requests with: uv add requests")
        return True

    base_url = "http://127.0.0.1:8000"

    # Test model list endpoint
    try:
        response = requests.get(f"{base_url}/api/models/list/", timeout=5)
        if response.status_code == 200:
            print("✓ Model list API working")
            data = response.json()
            if data.get('success'):
                models = data.get('models', {})
                print(f"  - Available model categories: {list(models.keys())}")
            else:
                print("  - API returned success=False")
        else:
            print(f"✗ Model list API failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"✗ Model list API error: {e}")
    except Exception as e:
        print(f"✗ API test error: {e}")

    return True

def test_performance_metrics():
    """Test performance characteristics"""
    print("\nTesting performance metrics...")
    
    # Test multiple face detections to measure performance
    test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    _, img_encoded = cv2.imencode('.jpg', test_image)
    img_bytes = img_encoded.tobytes()
    
    times = []
    for i in range(10):
        start_time = time.time()
        result = face_tracker.detect_face_landmarks(img_bytes)
        processing_time = (time.time() - start_time) * 1000
        times.append(processing_time)
    
    avg_time = sum(times) / len(times)
    min_time = min(times)
    max_time = max(times)
    
    print(f"✓ Performance test completed:")
    print(f"  - Average processing time: {avg_time:.2f}ms")
    print(f"  - Min processing time: {min_time:.2f}ms")
    print(f"  - Max processing time: {max_time:.2f}ms")
    print(f"  - Theoretical max FPS: {1000/avg_time:.1f}")
    
    # Performance recommendations
    if avg_time < 100:
        print("  - ✓ Excellent performance for real-time use")
    elif avg_time < 200:
        print("  - ✓ Good performance for real-time use")
    elif avg_time < 500:
        print("  - ⚠ Acceptable performance, consider optimization")
    else:
        print("  - ✗ Poor performance, optimization needed")
    
    return True

def test_system_requirements():
    """Test system requirements and dependencies"""
    print("\nTesting system requirements...")
    
    # Test OpenCV
    try:
        import cv2
        print(f"✓ OpenCV version: {cv2.__version__}")
    except ImportError:
        print("✗ OpenCV not available")
    
    # Test MediaPipe
    try:
        import mediapipe as mp
        print(f"✓ MediaPipe available")
    except ImportError:
        print("✗ MediaPipe not available")
    
    # Test NumPy
    try:
        import numpy as np
        print(f"✓ NumPy version: {np.__version__}")
    except ImportError:
        print("✗ NumPy not available")
    
    # Test Django
    try:
        import django
        print(f"✓ Django version: {django.__version__}")
    except ImportError:
        print("✗ Django not available")
    
    return True

def main():
    """Run all tests"""
    print("=" * 50)
    print("Virtual Try-On System Test Suite")
    print("=" * 50)
    
    tests = [
        test_system_requirements,
        test_face_detection,
        test_model_management,
        test_api_endpoints,
        test_performance_metrics,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with error: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready for use.")
    else:
        print("⚠ Some tests failed. Please check the issues above.")
    
    print("\nTo start the development server:")
    print("python manage.py runserver")
    print("\nThen visit: http://127.0.0.1:8000/")

if __name__ == "__main__":
    main()
