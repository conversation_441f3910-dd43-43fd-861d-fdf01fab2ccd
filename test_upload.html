<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; }
        input, select, button { padding: 8px; margin: 5px 0; }
        button { background: #007cba; color: white; border: none; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        .error { background: #ffe6e6; border-color: #ff0000; }
        .success { background: #e6ffe6; border-color: #00ff00; }
    </style>
</head>
<body>
    <h1>GLB/GLTF Upload Test</h1>
    
    <div class="form-group">
        <button onclick="testCategories()">Test Categories API</button>
        <button onclick="testModelsList()">Test Models List API</button>
    </div>
    
    <form id="uploadForm">
        <div class="form-group">
            <label for="modelFile">Select GLB/GLTF File:</label>
            <input type="file" id="modelFile" accept=".glb,.gltf" required>
        </div>
        
        <div class="form-group">
            <label for="modelName">Model Name:</label>
            <input type="text" id="modelName" required>
        </div>
        
        <div class="form-group">
            <label for="modelCategory">Category:</label>
            <select id="modelCategory" required>
                <option value="">Select Category</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="modelDescription">Description (optional):</label>
            <input type="text" id="modelDescription">
        </div>
        
        <button type="submit">Upload Model</button>
    </form>
    
    <div id="result" class="result" style="display: none;"></div>
    
    <script>
        // Get CSRF token
        function getCsrfToken() {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'csrftoken') {
                    return value;
                }
            }
            return '';
        }
        
        // Show result
        function showResult(message, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = isError ? 'result error' : 'result success';
            resultDiv.style.display = 'block';
            console.log(message);
        }
        
        // Test categories API
        async function testCategories() {
            try {
                const response = await fetch('/api/models/categories/');
                const categories = await response.json();
                
                if (response.ok) {
                    showResult(`Categories loaded: ${categories.length} found`);
                    
                    // Populate category select
                    const select = document.getElementById('modelCategory');
                    select.innerHTML = '<option value="">Select Category</option>';
                    
                    categories.forEach(category => {
                        if (category.is_active) {
                            const option = document.createElement('option');
                            option.value = category.id;
                            option.textContent = category.name;
                            select.appendChild(option);
                        }
                    });
                    
                    console.log('Categories:', categories);
                } else {
                    showResult(`Categories API error: ${response.status}`, true);
                }
            } catch (error) {
                showResult(`Categories API exception: ${error.message}`, true);
            }
        }
        
        // Test models list API
        async function testModelsList() {
            try {
                const response = await fetch('/api/models/list/');
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`Models list loaded: ${JSON.stringify(data)}`);
                    console.log('Models:', data);
                } else {
                    showResult(`Models list API error: ${response.status}`, true);
                }
            } catch (error) {
                showResult(`Models list API exception: ${error.message}`, true);
            }
        }
        
        // Handle form submission
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const fileInput = document.getElementById('modelFile');
            const nameInput = document.getElementById('modelName');
            const categorySelect = document.getElementById('modelCategory');
            const descriptionInput = document.getElementById('modelDescription');
            
            const file = fileInput.files[0];
            const name = nameInput.value.trim();
            const category = categorySelect.value;
            const description = descriptionInput.value.trim();
            
            if (!file || !name || !category) {
                showResult('Please fill in all required fields', true);
                return;
            }
            
            try {
                showResult('Uploading...');
                
                const formData = new FormData();
                formData.append('model_file', file);
                formData.append('name', name);
                formData.append('category', category);
                formData.append('description', description);
                
                const csrfToken = getCsrfToken();
                console.log('CSRF Token:', csrfToken);
                console.log('Upload data:', { name, category, description, fileName: file.name, fileSize: file.size });
                
                const response = await fetch('/api/models/models/', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRFToken': csrfToken
                    }
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                
                const responseText = await response.text();
                console.log('Response text:', responseText);
                
                if (response.ok) {
                    const result = JSON.parse(responseText);
                    showResult(`Upload successful! Model ID: ${result.id}`);
                    
                    // Clear form
                    fileInput.value = '';
                    nameInput.value = '';
                    categorySelect.value = '';
                    descriptionInput.value = '';
                } else {
                    let errorMessage = `Upload failed: ${response.status} ${response.statusText}`;
                    
                    try {
                        const errorData = JSON.parse(responseText);
                        if (errorData.model_file) {
                            errorMessage = errorData.model_file[0];
                        } else if (errorData.detail) {
                            errorMessage = errorData.detail;
                        } else if (errorData.error) {
                            errorMessage = errorData.error;
                        }
                    } catch (e) {
                        // Use default error message
                    }
                    
                    showResult(errorMessage, true);
                }
            } catch (error) {
                showResult(`Upload exception: ${error.message}`, true);
            }
        });
        
        // Load categories on page load
        window.addEventListener('load', () => {
            testCategories();
        });
    </script>
</body>
</html>
