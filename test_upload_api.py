#!/usr/bin/env python3
"""
Test script for upload API endpoints
"""

import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'virtual_try_on.settings')
django.setup()

from models_manager.models import AccessoryCategory, AccessoryModel

def test_categories():
    """Test category creation and API"""
    print("Testing categories...")
    
    # Create categories if they don't exist
    glasses_cat, created = AccessoryCategory.objects.get_or_create(
        slug='glasses',
        defaults={
            'name': 'Glasses',
            'description': 'Eyewear and sunglasses',
            'icon': '👓',
            'sort_order': 1
        }
    )
    if created:
        print(f"Created category: {glasses_cat.name}")
    
    hats_cat, created = AccessoryCategory.objects.get_or_create(
        slug='hats',
        defaults={
            'name': 'Hats',
            'description': 'Hats and headwear',
            'icon': '🎩',
            'sort_order': 2
        }
    )
    if created:
        print(f"Created category: {hats_cat.name}")
    
    # Test API endpoint
    try:
        response = requests.get('http://127.0.0.1:8000/api/models/categories/')
        print(f"Categories API status: {response.status_code}")
        if response.status_code == 200:
            categories = response.json()
            print(f"Categories found: {len(categories)}")
            for cat in categories:
                print(f"  - {cat['name']} (ID: {cat['id']}, Slug: {cat['slug']})")
            return categories
        else:
            print(f"Categories API error: {response.text}")
    except Exception as e:
        print(f"Categories API exception: {e}")
    
    return []

def test_models_list():
    """Test models list API"""
    print("\nTesting models list...")
    
    try:
        response = requests.get('http://127.0.0.1:8000/api/models/list/')
        print(f"Models list API status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Models list response: {data}")
            return data
        else:
            print(f"Models list API error: {response.text}")
    except Exception as e:
        print(f"Models list API exception: {e}")
    
    return {}

def create_test_glb_file():
    """Create a minimal test GLB file"""
    print("\nCreating test GLB file...")
    
    # This is a minimal GLB file content (binary glTF)
    # In a real scenario, you'd use a proper 3D modeling tool
    test_glb_content = b'glTF\x02\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00JSON\x00\x00\x00\x00{"asset":{"version":"2.0"}}'
    
    test_file_path = 'test_model.glb'
    with open(test_file_path, 'wb') as f:
        f.write(test_glb_content)
    
    print(f"Created test file: {test_file_path}")
    return test_file_path

def test_upload(categories):
    """Test model upload"""
    print("\nTesting model upload...")
    
    if not categories:
        print("No categories available for upload test")
        return
    
    # Create test file
    test_file = create_test_glb_file()
    
    try:
        # Get CSRF token
        session = requests.Session()
        csrf_response = session.get('http://127.0.0.1:8000/')
        csrf_token = None
        
        if 'csrftoken' in session.cookies:
            csrf_token = session.cookies['csrftoken']
            print(f"CSRF token obtained: {csrf_token[:10]}...")
        else:
            print("Warning: No CSRF token found")
        
        # Prepare upload data
        category_id = categories[0]['id']
        files = {'model_file': open(test_file, 'rb')}
        data = {
            'name': 'Test Model',
            'category': category_id,
            'description': 'Test upload model'
        }
        headers = {}
        if csrf_token:
            headers['X-CSRFToken'] = csrf_token
        
        print(f"Uploading to category ID: {category_id}")
        
        # Upload
        response = session.post(
            'http://127.0.0.1:8000/api/models/models/',
            files=files,
            data=data,
            headers=headers
        )
        
        print(f"Upload response status: {response.status_code}")
        print(f"Upload response: {response.text}")
        
        files['model_file'].close()
        
        if response.status_code == 201:
            print("✓ Upload successful!")
            return True
        else:
            print("✗ Upload failed")
            return False
            
    except Exception as e:
        print(f"Upload exception: {e}")
        return False
    finally:
        # Clean up test file
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"Cleaned up test file: {test_file}")

def main():
    """Run all tests"""
    print("=" * 50)
    print("Upload API Test Suite")
    print("=" * 50)
    
    categories = test_categories()
    test_models_list()
    test_upload(categories)
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    main()
